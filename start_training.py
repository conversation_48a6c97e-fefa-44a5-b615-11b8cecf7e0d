#!/usr/bin/env python3
"""
南水北调水力调度大模型微调启动脚本
支持三阶段渐进式微调：L0基座优化 -> L1领域专业化 -> 南水北调专用化
"""

import os
import sys
import yaml
import argparse
import logging
import subprocess
from pathlib import Path
from typing import Dict, List
import torch
import psutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TrainingOrchestrator:
    """训练编排器"""
    
    def __init__(self, config_path: str = "training_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.project_root = Path(__file__).parent
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            sys.exit(1)
    
    def check_prerequisites(self) -> bool:
        """检查前置条件"""
        logger.info("检查系统前置条件...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            logger.error("需要Python 3.8或更高版本")
            return False
        
        # 检查GPU
        if not torch.cuda.is_available():
            logger.warning("未检测到CUDA，将使用CPU训练（速度较慢）")
        else:
            gpu_count = torch.cuda.device_count()
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"检测到 {gpu_count} 个GPU，显存: {gpu_memory:.1f}GB")
            
            required_memory = self._parse_memory(self.config['hardware']['gpu']['required_memory'])
            if gpu_memory < required_memory:
                logger.warning(f"GPU显存不足，建议至少 {required_memory}GB")
        
        # 检查内存
        memory = psutil.virtual_memory()
        required_memory_gb = self._parse_memory(self.config['hardware']['cpu']['memory'])
        if memory.total / 1024**3 < required_memory_gb:
            logger.warning(f"系统内存不足，建议至少 {required_memory_gb}GB")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        required_storage_gb = self._parse_memory(self.config['hardware']['storage']['model_storage'])
        if disk.free / 1024**3 < required_storage_gb:
            logger.warning(f"磁盘空间不足，建议至少 {required_storage_gb}GB 可用空间")
        
        # 检查依赖包
        required_packages = [
            'torch', 'transformers', 'peft', 'datasets', 
            'accelerate', 'deepspeed', 'tensorboard'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"缺少依赖包: {missing_packages}")
            logger.info("请运行: pip install -r requirements_framework.txt")
            return False
        
        logger.info("前置条件检查通过")
        return True
    
    def _parse_memory(self, memory_str: str) -> float:
        """解析内存字符串，返回GB数值"""
        if memory_str.endswith('GB'):
            return float(memory_str[:-2])
        elif memory_str.endswith('MB'):
            return float(memory_str[:-2]) / 1024
        else:
            return float(memory_str)
    
    def prepare_environment(self):
        """准备训练环境"""
        logger.info("准备训练环境...")
        
        # 创建必要目录
        directories = [
            "models", "data", "logs", "checkpoints", 
            "test_data", "knowledge", "results"
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
            logger.info(f"创建目录: {directory}")
        
        # 设置环境变量
        os.environ['TOKENIZERS_PARALLELISM'] = 'false'
        os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
        
        # 配置PyTorch
        if torch.cuda.is_available():
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
    
    def run_stage(self, stage_name: str) -> bool:
        """运行指定阶段的训练"""
        logger.info(f"开始执行阶段: {stage_name}")
        
        stage_config = self.config['stages'][stage_name]
        
        if not stage_config.get('enabled', True):
            logger.info(f"阶段 {stage_name} 已禁用，跳过")
            return True
        
        try:
            if stage_name == 'l0_base_optimization':
                return self._run_l0_training(stage_config)
            elif stage_name == 'l1_domain_specialization':
                return self._run_l1_training(stage_config)
            elif stage_name == 'stn_specialized_model':
                return self._run_stn_training(stage_config)
            else:
                logger.error(f"未知阶段: {stage_name}")
                return False
                
        except Exception as e:
            logger.error(f"阶段 {stage_name} 执行失败: {e}")
            return False
    
    def _run_l0_training(self, config: Dict) -> bool:
        """运行L0基座优化训练"""
        logger.info("执行L0基座模型优化...")
        
        # 构建训练命令
        cmd = [
            sys.executable, "model_training_pipeline.py",
            "--stage", "l0",
            "--config", self.config_path,
            "--output-dir", config['output']['model_dir']
        ]
        
        # 添加模型参数
        cmd.extend([
            "--base-model", config['model']['base_model'],
            "--max-length", str(config['model']['max_length'])
        ])
        
        # 添加训练参数
        training_config = config['training']
        cmd.extend([
            "--num-epochs", str(training_config['hyperparameters']['num_epochs']),
            "--batch-size", str(training_config['hyperparameters']['batch_size']),
            "--learning-rate", str(training_config['hyperparameters']['learning_rate'])
        ])
        
        return self._execute_command(cmd)
    
    def _run_l1_training(self, config: Dict) -> bool:
        """运行L1领域专业化训练"""
        logger.info("执行L1水利领域专业化...")
        
        cmd = [
            sys.executable, "model_training_pipeline.py",
            "--stage", "l1",
            "--config", self.config_path,
            "--base-model", config['model']['base_model'],
            "--output-dir", config['output']['model_dir']
        ]
        
        return self._execute_command(cmd)
    
    def _run_stn_training(self, config: Dict) -> bool:
        """运行南水北调专用模型训练"""
        logger.info("执行南水北调专用模型训练...")
        
        cmd = [
            sys.executable, "model_training_pipeline.py",
            "--stage", "stn",
            "--config", self.config_path,
            "--base-model", config['model']['base_model'],
            "--output-dir", config['output']['model_dir']
        ]
        
        return self._execute_command(cmd)
    
    def _execute_command(self, cmd: List[str]) -> bool:
        """执行命令"""
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("命令执行成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"命令执行失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            return False
    
    def run_evaluation(self, model_path: str) -> Dict:
        """运行模型评估"""
        logger.info(f"评估模型: {model_path}")
        
        # 这里可以调用评估脚本
        eval_cmd = [
            sys.executable, "evaluate_model.py",
            "--model-path", model_path,
            "--test-data", "./test_data",
            "--output", f"{model_path}/evaluation_results.json"
        ]
        
        if self._execute_command(eval_cmd):
            # 读取评估结果
            try:
                import json
                with open(f"{model_path}/evaluation_results.json", 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"读取评估结果失败: {e}")
                return {}
        
        return {}
    
    def generate_report(self, results: Dict):
        """生成训练报告"""
        logger.info("生成训练报告...")
        
        report = f"""
# 南水北调水力调度大模型训练报告

## 训练概述
- 项目名称: {self.config['project']['name']}
- 版本: {self.config['project']['version']}
- 训练时间: {results.get('training_time', 'N/A')}

## 模型配置
- 基础模型: {self.config['stages']['l0_base_optimization']['model']['base_model']}
- 最大长度: {self.config['stages']['l0_base_optimization']['model']['max_length']}

## 训练结果
- L0模型路径: {self.config['stages']['l0_base_optimization']['output']['model_dir']}
- L1模型路径: {self.config['stages']['l1_domain_specialization']['output']['model_dir']}
- 最终模型路径: {self.config['stages']['stn_specialized_model']['output']['model_dir']}

## 评估指标
{self._format_evaluation_results(results.get('evaluation', {}))}

## 部署建议
1. 使用最终模型替换现有RAG系统的LLM组件
2. 保留现有的检索和向量化能力
3. 逐步迁移用户流量，监控性能表现
4. 建立反馈机制，持续优化模型效果

## 后续优化方向
1. 收集更多南水北调专业数据
2. 增强多模态处理能力
3. 优化推理速度和资源消耗
4. 建立在线学习机制
"""
        
        # 保存报告
        with open("training_report.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        logger.info("训练报告已保存至: training_report.md")
    
    def _format_evaluation_results(self, eval_results: Dict) -> str:
        """格式化评估结果"""
        if not eval_results:
            return "暂无评估结果"
        
        formatted = ""
        for metric, value in eval_results.items():
            if isinstance(value, float):
                formatted += f"- {metric}: {value:.4f}\n"
            else:
                formatted += f"- {metric}: {value}\n"
        
        return formatted

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="南水北调水力调度大模型微调启动脚本")
    parser.add_argument("--config", default="training_config.yaml", help="配置文件路径")
    parser.add_argument("--stage", choices=["all", "l0", "l1", "stn"], default="all", help="执行阶段")
    parser.add_argument("--skip-check", action="store_true", help="跳过前置条件检查")
    parser.add_argument("--eval-only", action="store_true", help="仅运行评估")
    
    args = parser.parse_args()
    
    # 创建训练编排器
    orchestrator = TrainingOrchestrator(args.config)
    
    # 检查前置条件
    if not args.skip_check and not orchestrator.check_prerequisites():
        logger.error("前置条件检查失败，请解决问题后重试")
        sys.exit(1)
    
    # 准备环境
    orchestrator.prepare_environment()
    
    # 执行训练或评估
    if args.eval_only:
        # 仅运行评估
        model_path = orchestrator.config['stages']['stn_specialized_model']['output']['model_dir']
        eval_results = orchestrator.run_evaluation(model_path)
        orchestrator.generate_report({"evaluation": eval_results})
    else:
        # 执行训练
        results = {"training_time": "N/A", "evaluation": {}}
        
        if args.stage == "all":
            # 执行所有阶段
            stages = ['l0_base_optimization', 'l1_domain_specialization', 'stn_specialized_model']
        else:
            # 执行指定阶段
            stage_map = {
                'l0': 'l0_base_optimization',
                'l1': 'l1_domain_specialization', 
                'stn': 'stn_specialized_model'
            }
            stages = [stage_map[args.stage]]
        
        # 逐阶段执行
        for stage in stages:
            success = orchestrator.run_stage(stage)
            if not success:
                logger.error(f"阶段 {stage} 失败，停止训练")
                sys.exit(1)
        
        # 运行最终评估
        final_model_path = orchestrator.config['stages']['stn_specialized_model']['output']['model_dir']
        eval_results = orchestrator.run_evaluation(final_model_path)
        results["evaluation"] = eval_results
        
        # 生成报告
        orchestrator.generate_report(results)
    
    logger.info("训练流程完成！")
    print("\n" + "="*60)
    print("🎉 南水北调水力调度大模型微调完成！")
    print("="*60)
    print("📊 查看训练报告: training_report.md")
    print("📁 模型文件位置: ./models/")
    print("📈 训练日志: training.log")
    print("="*60)

if __name__ == "__main__":
    main()

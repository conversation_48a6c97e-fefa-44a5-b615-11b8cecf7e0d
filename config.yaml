# 南水北调水力调度大模型框架配置文件

# 系统基础配置
system:
  name: "南水北调水力调度AI框架"
  version: "1.0.0"
  environment: "development"  # development, testing, production
  log_level: "INFO"
  debug: true

# 模型配置
models:
  # L0基座模型配置
  l0_base:
    embedding_model: "BAAI/bge-large-zh-v1.5"
    rerank_model: "BAAI/bge-reranker-base"
    llm_model: "deepseek-chat"
    device: "cuda"  # cuda, cpu, auto
    
  # L1水利领域模型配置
  l1_domain:
    model_path: "./models/l1_water_conservancy"
    knowledge_base_path: "./knowledge/water_conservancy"
    fine_tune_data_path: "./data/domain_training"
    
  # 南水北调专用模型配置
  stn_specialized:
    model_path: "./models/stn_scheduling"
    knowledge_base_path: "./knowledge/south_to_north"
    operational_data_path: "./data/stn_operations"

# 数据存储配置
storage:
  # 向量数据库
  vector_store:
    type: "chromadb"
    path: "./storage/chroma_db"
    collection_name: "water_conservancy_knowledge"
    
  # 时序数据库
  timeseries_store:
    type: "influxdb"
    host: "localhost"
    port: 8086
    database: "water_monitoring"
    
  # 关系数据库
  relational_store:
    type: "postgresql"
    host: "localhost"
    port: 5432
    database: "water_management"
    username: "water_admin"
    password: "secure_password"

# 数据处理配置
data_processing:
  # 文本处理
  text:
    chunk_size: 1024
    chunk_overlap: 200
    semantic_threshold: 95
    max_context_length: 25000
    
  # 图像处理
  image:
    ocr_engine: "mineru"
    image_encoder: "clip"
    max_image_size: "2048x2048"
    supported_formats: ["jpg", "png", "pdf"]
    
  # 时序数据处理
  timeseries:
    sampling_rate: "1min"
    aggregation_window: "1hour"
    anomaly_threshold: 3.0
    pattern_detection: true

# 多模态融合配置
multimodal_fusion:
  fusion_method: "cross_attention"
  modality_weights:
    text: 0.4
    image: 0.3
    timeseries: 0.3
  feature_dimensions:
    text: 1024
    image: 512
    timeseries: 256

# 调度优化配置
scheduling:
  # 优化目标权重
  objective_weights:
    minimize_cost: 0.25
    maximize_efficiency: 0.25
    minimize_risk: 0.30
    satisfy_demand: 0.20
    
  # 约束条件
  constraints:
    min_flow_velocity: 0.5  # m/s
    max_flow_velocity: 2.0  # m/s
    min_water_level: 100.0  # m
    max_water_level: 110.0  # m
    max_gate_opening: 95    # %
    
  # 时间配置
  planning_horizon: 7      # days
  update_frequency: 1      # hours
  emergency_response_time: 15  # minutes

# 风险评估配置
risk_assessment:
  risk_factors:
    operational: 0.3
    weather: 0.3
    equipment: 0.2
    demand: 0.2
    
  risk_thresholds:
    low: 0.3
    medium: 0.7
    high: 1.0
    
  monitoring_intervals:
    normal: 60      # minutes
    elevated: 30    # minutes
    high: 15        # minutes

# 应急响应配置
emergency_response:
  # 触发条件
  triggers:
    extreme_weather:
      precipitation_threshold: 100  # mm
      temperature_threshold: -10    # °C
      wind_speed_threshold: 25      # m/s
      
    equipment_failure:
      health_threshold: 0.3
      response_time: 30  # minutes
      
    water_quality:
      ph_range: [6.5, 8.5]
      turbidity_threshold: 10  # NTU
      
  # 响应级别
  response_levels:
    level_1:  # 轻微
      notification_delay: 60    # minutes
      action_required: false
      
    level_2:  # 中等
      notification_delay: 30    # minutes
      action_required: true
      escalation_time: 120      # minutes
      
    level_3:  # 严重
      notification_delay: 5     # minutes
      action_required: true
      escalation_time: 30       # minutes

# API配置
api:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  timeout: 300
  rate_limit: "100/minute"
  
  # 认证配置
  authentication:
    enabled: true
    method: "jwt"
    secret_key: "your_secret_key_here"
    token_expiry: 3600  # seconds
    
  # CORS配置
  cors:
    enabled: true
    origins: ["*"]
    methods: ["GET", "POST", "PUT", "DELETE"]

# 监控配置
monitoring:
  # 性能监控
  performance:
    response_time_threshold: 1000  # ms
    memory_usage_threshold: 80     # %
    cpu_usage_threshold: 70        # %
    
  # 业务监控
  business:
    prediction_accuracy_threshold: 0.95
    decision_confidence_threshold: 0.8
    system_availability_threshold: 0.999
    
  # 告警配置
  alerts:
    email:
      enabled: true
      smtp_server: "smtp.example.com"
      recipients: ["<EMAIL>"]
      
    sms:
      enabled: false
      provider: "twilio"
      
    webhook:
      enabled: true
      url: "https://hooks.slack.com/services/..."

# 南水北调工程特定配置
south_to_north_project:
  # 工程基本信息
  project_info:
    total_length: 1432      # km
    design_capacity: 350    # m³/s
    annual_transfer: 95     # 亿m³
    
  # 主要控制节点
  control_nodes:
    taocha_intake:
      location: "河南省淅川县"
      max_flow: 350         # m³/s
      coordinates: [111.5, 33.1]
      
    main_canal_segments:
      - name: "渠首段"
        length: 50          # km
        capacity: 350       # m³/s
        
      - name: "河南段"
        length: 731         # km
        capacity: 350       # m³/s
        
      - name: "河北段"
        length: 482         # km
        capacity: 350       # m³/s
        
      - name: "北京段"
        length: 80          # km
        capacity: 50        # m³/s
        
      - name: "天津段"
        length: 89          # km
        capacity: 30        # m³/s
  
  # 受水区配置
  water_users:
    beijing:
      annual_demand: 12.4   # 亿m³
      priority: 1
      population: 21000000
      
    tianjin:
      annual_demand: 8.6    # 亿m³
      priority: 2
      population: 15000000
      
    hebei:
      annual_demand: 21.0   # 亿m³
      priority: 3
      population: 75000000
      
    henan:
      annual_demand: 37.7   # 亿m³
      priority: 4
      population: 99000000

# 开发和测试配置
development:
  # 测试数据配置
  test_data:
    synthetic_data_enabled: true
    historical_data_path: "./data/historical"
    simulation_mode: true
    
  # 调试配置
  debug:
    verbose_logging: true
    profiling_enabled: true
    mock_external_apis: true
    
# 生产环境配置
production:
  # 高可用配置
  high_availability:
    load_balancer: true
    backup_instances: 2
    failover_timeout: 30    # seconds
    
  # 安全配置
  security:
    encryption_enabled: true
    ssl_certificate_path: "/etc/ssl/certs/app.crt"
    ssl_private_key_path: "/etc/ssl/private/app.key"
    firewall_rules: ["10.0.0.0/8", "172.16.0.0/12"]
    
  # 备份配置
  backup:
    enabled: true
    frequency: "daily"
    retention_days: 30
    storage_location: "s3://backup-bucket/water-ai"

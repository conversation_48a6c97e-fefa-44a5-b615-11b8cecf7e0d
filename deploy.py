#!/usr/bin/env python3
"""
南水北调水力调度大模型框架部署脚本
支持开发、测试、生产环境的自动化部署
"""

import os
import sys
import yaml
import argparse
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Optional
import docker
import psutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DeploymentManager:
    """部署管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.docker_client = docker.from_env()
        self.project_root = Path(__file__).parent
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            sys.exit(1)
    
    def check_system_requirements(self) -> bool:
        """检查系统要求"""
        logger.info("检查系统要求...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version.major < 3 or python_version.minor < 8:
            logger.error("需要Python 3.8或更高版本")
            return False
        
        # 检查内存
        memory = psutil.virtual_memory()
        if memory.total < 8 * 1024 * 1024 * 1024:  # 8GB
            logger.warning("建议至少8GB内存")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        if disk.free < 50 * 1024 * 1024 * 1024:  # 50GB
            logger.warning("建议至少50GB可用磁盘空间")
        
        # 检查CUDA（如果配置了GPU）
        if self.config['models']['l0_base']['device'] == 'cuda':
            try:
                import torch
                if not torch.cuda.is_available():
                    logger.warning("CUDA不可用，将使用CPU模式")
                    self.config['models']['l0_base']['device'] = 'cpu'
            except ImportError:
                logger.warning("PyTorch未安装，无法检查CUDA")
        
        logger.info("系统要求检查完成")
        return True
    
    def setup_environment(self, env_type: str = "development"):
        """设置环境"""
        logger.info(f"设置{env_type}环境...")
        
        # 创建必要的目录
        directories = [
            "storage/chroma_db",
            "storage/models",
            "storage/logs",
            "data/historical",
            "data/realtime",
            "knowledge/water_conservancy",
            "knowledge/south_to_north",
            "models/l1_water_conservancy",
            "models/stn_scheduling"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {directory}")
        
        # 设置环境变量
        env_vars = {
            'PYTHONPATH': str(self.project_root),
            'WATER_AI_ENV': env_type,
            'WATER_AI_CONFIG': self.config_path
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            logger.info(f"设置环境变量: {key}={value}")
    
    def install_dependencies(self):
        """安装依赖包"""
        logger.info("安装依赖包...")
        
        requirements_files = [
            "requirements_framework.txt",
            "requirements.txt"  # 如果存在原有的requirements.txt
        ]
        
        for req_file in requirements_files:
            if Path(req_file).exists():
                try:
                    subprocess.run([
                        sys.executable, "-m", "pip", "install", "-r", req_file
                    ], check=True)
                    logger.info(f"成功安装依赖: {req_file}")
                except subprocess.CalledProcessError as e:
                    logger.error(f"安装依赖失败: {e}")
                    return False
        
        return True
    
    def download_models(self):
        """下载预训练模型"""
        logger.info("下载预训练模型...")
        
        models_to_download = [
            self.config['models']['l0_base']['embedding_model'],
            self.config['models']['l0_base']['rerank_model']
        ]
        
        for model_name in models_to_download:
            try:
                from transformers import AutoModel, AutoTokenizer
                logger.info(f"下载模型: {model_name}")
                
                # 下载模型和分词器
                AutoModel.from_pretrained(model_name)
                AutoTokenizer.from_pretrained(model_name)
                
                logger.info(f"模型下载完成: {model_name}")
            except Exception as e:
                logger.error(f"模型下载失败 {model_name}: {e}")
                return False
        
        return True
    
    def setup_databases(self):
        """设置数据库"""
        logger.info("设置数据库...")
        
        # 初始化ChromaDB
        try:
            import chromadb
            client = chromadb.PersistentClient(
                path=self.config['storage']['vector_store']['path']
            )
            collection = client.get_or_create_collection(
                name=self.config['storage']['vector_store']['collection_name']
            )
            logger.info("ChromaDB初始化完成")
        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {e}")
            return False
        
        # 如果配置了PostgreSQL，创建数据库表
        if self.config['storage']['relational_store']['type'] == 'postgresql':
            try:
                self._setup_postgresql()
            except Exception as e:
                logger.warning(f"PostgreSQL设置失败: {e}")
        
        return True
    
    def _setup_postgresql(self):
        """设置PostgreSQL数据库"""
        # 这里可以添加PostgreSQL数据库表创建逻辑
        logger.info("PostgreSQL设置完成")
    
    def create_docker_compose(self, env_type: str):
        """创建Docker Compose配置"""
        logger.info("创建Docker Compose配置...")
        
        compose_config = {
            'version': '3.8',
            'services': {
                'water-ai-framework': {
                    'build': {
                        'context': '.',
                        'dockerfile': 'Dockerfile'
                    },
                    'ports': [
                        f"{self.config['api']['port']}:{self.config['api']['port']}"
                    ],
                    'environment': [
                        f"WATER_AI_ENV={env_type}",
                        f"WATER_AI_CONFIG={self.config_path}"
                    ],
                    'volumes': [
                        './storage:/app/storage',
                        './data:/app/data',
                        './knowledge:/app/knowledge',
                        './models:/app/models'
                    ],
                    'depends_on': ['redis', 'postgres']
                },
                'redis': {
                    'image': 'redis:7-alpine',
                    'ports': ['6379:6379']
                },
                'postgres': {
                    'image': 'postgres:15-alpine',
                    'environment': [
                        'POSTGRES_DB=water_management',
                        'POSTGRES_USER=water_admin',
                        'POSTGRES_PASSWORD=secure_password'
                    ],
                    'ports': ['5432:5432'],
                    'volumes': ['postgres_data:/var/lib/postgresql/data']
                }
            },
            'volumes': {
                'postgres_data': {}
            }
        }
        
        # 如果是生产环境，添加nginx
        if env_type == 'production':
            compose_config['services']['nginx'] = {
                'image': 'nginx:alpine',
                'ports': ['80:80', '443:443'],
                'volumes': [
                    './nginx.conf:/etc/nginx/nginx.conf',
                    './ssl:/etc/ssl'
                ],
                'depends_on': ['water-ai-framework']
            }
        
        # 保存配置文件
        with open('docker-compose.yml', 'w') as f:
            yaml.dump(compose_config, f, default_flow_style=False)
        
        logger.info("Docker Compose配置创建完成")
    
    def create_dockerfile(self):
        """创建Dockerfile"""
        logger.info("创建Dockerfile...")
        
        dockerfile_content = """
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    git \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements_framework.txt .
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements_framework.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 waterai && chown -R waterai:waterai /app
USER waterai

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "api:app", "--host", "0.0.0.0", "--port", "8000"]
"""
        
        with open('Dockerfile', 'w') as f:
            f.write(dockerfile_content.strip())
        
        logger.info("Dockerfile创建完成")
    
    def run_tests(self):
        """运行测试"""
        logger.info("运行测试...")
        
        try:
            # 运行单元测试
            subprocess.run([
                sys.executable, "-m", "pytest", "tests/", "-v"
            ], check=True)
            
            # 运行集成测试
            subprocess.run([
                sys.executable, "water_conservancy_ai_framework.py"
            ], check=True, timeout=60)
            
            logger.info("测试通过")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"测试失败: {e}")
            return False
        except subprocess.TimeoutExpired:
            logger.error("测试超时")
            return False
    
    def deploy(self, env_type: str = "development", skip_tests: bool = False):
        """执行部署"""
        logger.info(f"开始部署到{env_type}环境...")
        
        # 检查系统要求
        if not self.check_system_requirements():
            logger.error("系统要求检查失败")
            return False
        
        # 设置环境
        self.setup_environment(env_type)
        
        # 安装依赖
        if not self.install_dependencies():
            logger.error("依赖安装失败")
            return False
        
        # 下载模型
        if not self.download_models():
            logger.error("模型下载失败")
            return False
        
        # 设置数据库
        if not self.setup_databases():
            logger.error("数据库设置失败")
            return False
        
        # 运行测试
        if not skip_tests and not self.run_tests():
            logger.error("测试失败")
            return False
        
        # 创建Docker配置（如果需要）
        if env_type in ['testing', 'production']:
            self.create_dockerfile()
            self.create_docker_compose(env_type)
        
        logger.info(f"部署到{env_type}环境完成！")
        return True
    
    def start_services(self, env_type: str = "development"):
        """启动服务"""
        logger.info("启动服务...")
        
        if env_type == "development":
            # 开发环境直接启动
            try:
                subprocess.Popen([
                    sys.executable, "-m", "uvicorn", 
                    "api:app", "--reload", "--host", "0.0.0.0", 
                    "--port", str(self.config['api']['port'])
                ])
                logger.info(f"服务已启动，访问地址: http://localhost:{self.config['api']['port']}")
            except Exception as e:
                logger.error(f"服务启动失败: {e}")
                return False
        else:
            # 生产环境使用Docker Compose
            try:
                subprocess.run([
                    "docker-compose", "up", "-d"
                ], check=True)
                logger.info("Docker服务启动完成")
            except subprocess.CalledProcessError as e:
                logger.error(f"Docker服务启动失败: {e}")
                return False
        
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="南水北调水力调度大模型框架部署脚本")
    parser.add_argument(
        "--env", 
        choices=["development", "testing", "production"],
        default="development",
        help="部署环境"
    )
    parser.add_argument(
        "--config",
        default="config.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--skip-tests",
        action="store_true",
        help="跳过测试"
    )
    parser.add_argument(
        "--start",
        action="store_true",
        help="部署完成后启动服务"
    )
    
    args = parser.parse_args()
    
    # 创建部署管理器
    deployer = DeploymentManager(args.config)
    
    # 执行部署
    success = deployer.deploy(
        env_type=args.env,
        skip_tests=args.skip_tests
    )
    
    if success and args.start:
        deployer.start_services(args.env)
    
    if success:
        logger.info("部署成功完成！")
        print("\n" + "="*60)
        print("🎉 南水北调水力调度大模型框架部署成功！")
        print("="*60)
        print(f"环境: {args.env}")
        print(f"配置文件: {args.config}")
        if args.start:
            print(f"服务地址: http://localhost:{deployer.config['api']['port']}")
        print("="*60)
    else:
        logger.error("部署失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()

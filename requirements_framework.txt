# 南水北调水力调度大模型框架依赖包

# 核心AI框架
torch>=2.0.0
transformers>=4.30.0
llama-index-core>=0.10.0
llama-index-embeddings-huggingface>=0.1.0
llama-index-llms-huggingface>=0.1.0
llama-index-llms-openai-like>=0.1.0
llama-index-vector-stores-chroma>=0.1.0
llama-index-retrievers-bm25>=0.1.0
sentence-transformers>=2.2.2
huggingface-hub>=0.19.0

# 向量数据库
chromadb>=0.4.18

# 数据处理
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0
scikit-learn>=1.3.0

# 时序数据处理
influxdb-client>=1.36.0
tslearn>=0.6.0
statsmodels>=0.14.0

# 图像处理
opencv-python>=4.8.0
Pillow>=10.0.0
pdf2image>=1.16.3
pytesseract>=0.3.10

# 多模态处理
clip-by-openai>=1.0
timm>=0.9.0

# Web框架和API
fastapi>=0.100.0
uvicorn>=0.23.0
pydantic>=2.0.0
python-multipart>=0.0.6

# 数据库
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0
alembic>=1.11.0

# 配置管理
pyyaml>=6.0
python-dotenv>=1.0.0
hydra-core>=1.3.0

# 监控和日志
prometheus-client>=0.17.0
structlog>=23.1.0
loguru>=0.7.0

# 异步处理
asyncio>=3.4.3
aiofiles>=23.1.0
aiohttp>=3.8.0

# 工具库
tqdm>=4.65.0
click>=8.1.0
rich>=13.0.0
typer>=0.9.0

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
httpx>=0.24.0

# 安全
cryptography>=41.0.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# 部署和容器化
gunicorn>=21.0.0
docker>=6.1.0

# 开发工具
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0

# 可视化
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
streamlit>=1.31.0

# 地理信息系统
geopandas>=0.13.0
folium>=0.14.0
shapely>=2.0.0

# 优化算法
optuna>=3.3.0
scipy>=1.10.0
cvxpy>=1.3.0

# 知识图谱
neo4j>=5.11.0
rdflib>=7.0.0
networkx>=3.1.0

# 消息队列
celery>=5.3.0
redis>=4.6.0
kombu>=5.3.0

# 文件处理
openpyxl>=3.1.0
xlrd>=2.0.1
pypdf>=3.15.1
pdfplumber>=0.10.0

# 网络和通信
requests>=2.31.0
websockets>=11.0.0
paho-mqtt>=1.6.0

# 缓存
redis>=4.6.0
memcached>=1.59

# 配置和环境管理
python-decouple>=3.8
environs>=9.5.0

# 数据验证
marshmallow>=3.20.0
cerberus>=1.3.4

# 任务调度
apscheduler>=3.10.0
crontab>=1.0.0

# 性能分析
py-spy>=0.3.14
memory-profiler>=0.61.0
line-profiler>=4.1.0

# 文档生成
sphinx>=7.1.0
mkdocs>=1.5.0
mkdocs-material>=9.2.0

# 代码质量
pre-commit>=3.3.0
bandit>=1.7.5
safety>=2.3.0

# 机器学习扩展
xgboost>=1.7.0
lightgbm>=4.0.0
catboost>=1.2.0

# 深度学习扩展
pytorch-lightning>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# 自然语言处理
spacy>=3.6.0
jieba>=0.42.1
pypinyin>=0.49.0

# 时间处理
arrow>=1.2.3
pendulum>=2.1.2
python-dateutil>=2.8.2

# 并发处理
concurrent-futures>=3.1.1
multiprocessing-logging>=0.3.4

# 系统监控
psutil>=5.9.0
GPUtil>=1.4.0

# 网络安全
cryptography>=41.0.0
pyotp>=2.9.0

# 数据序列化
msgpack>=1.0.5
pickle5>=0.0.12
orjson>=3.9.0

# 国际化
babel>=2.12.0
gettext>=4.0

# 邮件发送
sendgrid>=6.10.0
smtplib>=3.11.0

# 短信发送
twilio>=8.5.0

# 云服务
boto3>=1.28.0
azure-storage-blob>=12.17.0
google-cloud-storage>=2.10.0

# 容器编排
kubernetes>=27.2.0
docker-compose>=1.29.0

# 服务发现
consul>=1.1.0
etcd3>=0.12.0

# 负载均衡
haproxy>=2.8.0

# 反向代理
nginx>=1.25.0

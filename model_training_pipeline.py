"""
南水北调水力调度大模型微调训练流水线
基于现有RAG系统的渐进式微调方案
"""

import os
import json
import torch
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

import pandas as pd
from datasets import Dataset, DatasetDict
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType, PeftModel
import yaml

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TrainingConfig:
    """训练配置"""
    # 模型配置
    base_model_name: str = "Qwen/Qwen2.5-7B-Instruct"
    max_length: int = 2048
    
    # LoRA配置
    lora_r: int = 64
    lora_alpha: int = 128
    lora_dropout: float = 0.1
    
    # 训练配置
    num_epochs: int = 3
    batch_size: int = 4
    gradient_accumulation_steps: int = 8
    learning_rate: float = 2e-4
    warmup_ratio: float = 0.1
    
    # 输出配置
    output_dir: str = "./models/water_ai_finetuned"
    logging_steps: int = 100
    save_steps: int = 500
    eval_steps: int = 500

class WaterConservancyDataProcessor:
    """水利数据处理器"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.tokenizer = AutoTokenizer.from_pretrained(config.base_model_name)
        
        # 设置特殊token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def load_existing_rag_data(self, data_dir: str) -> List[Dict]:
        """加载现有RAG系统的数据"""
        logger.info(f"从 {data_dir} 加载现有RAG数据...")
        
        all_data = []
        
        # 加载JSON数据
        json_files = list(Path(data_dir).glob("*.json"))
        for json_file in json_files:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    all_data.extend(data)
                else:
                    all_data.append(data)
        
        # 加载Excel数据（转换为文本）
        excel_files = list(Path(data_dir).glob("*.xlsx"))
        for excel_file in excel_files:
            df = pd.read_excel(excel_file)
            for _, row in df.iterrows():
                text_content = " ".join([f"{col}: {val}" for col, val in row.items() if pd.notna(val)])
                all_data.append({
                    "text": text_content,
                    "source": str(excel_file.name),
                    "type": "excel_data"
                })
        
        logger.info(f"加载了 {len(all_data)} 条数据")
        return all_data
    
    def create_instruction_data(self, raw_data: List[Dict]) -> List[Dict]:
        """创建指令微调数据"""
        logger.info("创建指令微调数据...")
        
        instruction_data = []
        
        # 指令模板
        templates = [
            {
                "instruction": "请根据以下水利工程信息回答问题：{context}\n\n问题：{question}",
                "response_prefix": "根据提供的信息，"
            },
            {
                "instruction": "作为水利工程专家，请分析以下情况：{context}",
                "response_prefix": "基于水利工程专业知识，"
            },
            {
                "instruction": "请解释以下水利术语或概念：{term}",
                "response_prefix": "该术语的含义是："
            }
        ]
        
        for item in raw_data:
            if isinstance(item, dict) and 'text' in item:
                text = item['text']
                
                # 生成问答对
                qa_pairs = self._generate_qa_pairs(text)
                
                for qa in qa_pairs:
                    template = templates[0]  # 使用第一个模板
                    
                    instruction_item = {
                        "instruction": template["instruction"].format(
                            context=text[:500],  # 限制上下文长度
                            question=qa["question"]
                        ),
                        "input": "",
                        "output": template["response_prefix"] + qa["answer"],
                        "source": item.get("source", "unknown")
                    }
                    instruction_data.append(instruction_item)
        
        logger.info(f"生成了 {len(instruction_data)} 条指令数据")
        return instruction_data
    
    def _generate_qa_pairs(self, text: str) -> List[Dict]:
        """从文本生成问答对"""
        # 简化的问答对生成逻辑
        qa_pairs = []
        
        # 基于关键词生成问题
        water_keywords = ["水位", "流量", "闸门", "调度", "水质", "监测"]
        
        for keyword in water_keywords:
            if keyword in text:
                question = f"关于{keyword}的情况如何？"
                # 提取包含关键词的句子作为答案
                sentences = text.split('。')
                answer = ""
                for sentence in sentences:
                    if keyword in sentence:
                        answer = sentence.strip() + "。"
                        break
                
                if answer:
                    qa_pairs.append({
                        "question": question,
                        "answer": answer
                    })
        
        return qa_pairs[:3]  # 限制每个文本最多3个问答对
    
    def prepare_training_data(self, instruction_data: List[Dict]) -> DatasetDict:
        """准备训练数据"""
        logger.info("准备训练数据...")
        
        # 格式化数据
        formatted_data = []
        for item in instruction_data:
            # 构建训练文本
            if item["input"]:
                text = f"### 指令:\n{item['instruction']}\n\n### 输入:\n{item['input']}\n\n### 回答:\n{item['output']}"
            else:
                text = f"### 指令:\n{item['instruction']}\n\n### 回答:\n{item['output']}"
            
            formatted_data.append({"text": text})
        
        # 分割训练集和验证集
        split_idx = int(len(formatted_data) * 0.9)
        train_data = formatted_data[:split_idx]
        eval_data = formatted_data[split_idx:]
        
        # 创建Dataset
        train_dataset = Dataset.from_list(train_data)
        eval_dataset = Dataset.from_list(eval_data)
        
        # Tokenize数据
        def tokenize_function(examples):
            return self.tokenizer(
                examples["text"],
                truncation=True,
                padding=False,
                max_length=self.config.max_length,
                return_overflowing_tokens=False,
            )
        
        train_dataset = train_dataset.map(tokenize_function, batched=True)
        eval_dataset = eval_dataset.map(tokenize_function, batched=True)
        
        return DatasetDict({
            "train": train_dataset,
            "eval": eval_dataset
        })

class WaterConservancyTrainer:
    """水利大模型训练器"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.tokenizer = AutoTokenizer.from_pretrained(config.base_model_name)
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def load_base_model(self) -> torch.nn.Module:
        """加载基础模型"""
        logger.info(f"加载基础模型: {self.config.base_model_name}")
        
        model = AutoModelForCausalLM.from_pretrained(
            self.config.base_model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        
        return model
    
    def setup_lora(self, model: torch.nn.Module) -> torch.nn.Module:
        """设置LoRA微调"""
        logger.info("设置LoRA微调配置...")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=self.config.lora_r,
            lora_alpha=self.config.lora_alpha,
            lora_dropout=self.config.lora_dropout,
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            bias="none",
        )
        
        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()
        
        return model
    
    def train(self, model: torch.nn.Module, dataset: DatasetDict) -> None:
        """执行模型训练"""
        logger.info("开始模型训练...")
        
        # 训练参数
        training_args = TrainingArguments(
            output_dir=self.config.output_dir,
            overwrite_output_dir=True,
            
            num_train_epochs=self.config.num_epochs,
            per_device_train_batch_size=self.config.batch_size,
            per_device_eval_batch_size=self.config.batch_size,
            gradient_accumulation_steps=self.config.gradient_accumulation_steps,
            
            learning_rate=self.config.learning_rate,
            weight_decay=0.01,
            warmup_ratio=self.config.warmup_ratio,
            lr_scheduler_type="cosine",
            
            logging_steps=self.config.logging_steps,
            save_steps=self.config.save_steps,
            eval_steps=self.config.eval_steps,
            evaluation_strategy="steps",
            save_strategy="steps",
            
            fp16=True,
            dataloader_pin_memory=True,
            gradient_checkpointing=True,
            
            report_to="tensorboard",
            run_name=f"water_ai_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
        )
        
        # 数据整理器
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # 创建训练器
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["eval"],
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )
        
        # 开始训练
        trainer.train()
        
        # 保存最终模型
        trainer.save_model()
        self.tokenizer.save_pretrained(self.config.output_dir)
        
        logger.info(f"训练完成，模型保存至: {self.config.output_dir}")

class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = PeftModel.from_pretrained(
            AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                device_map="auto"
            ),
            model_path
        )
    
    def evaluate_on_test_set(self, test_data: List[Dict]) -> Dict:
        """在测试集上评估模型"""
        logger.info("在测试集上评估模型...")
        
        results = {
            "total_samples": len(test_data),
            "correct_predictions": 0,
            "average_response_length": 0,
            "sample_responses": []
        }
        
        total_length = 0
        
        for i, item in enumerate(test_data[:10]):  # 评估前10个样本
            instruction = item["instruction"]
            expected_output = item["output"]
            
            # 生成回答
            generated_output = self._generate_response(instruction)
            
            # 简单的正确性检查（可以改进）
            is_correct = self._check_correctness(generated_output, expected_output)
            if is_correct:
                results["correct_predictions"] += 1
            
            total_length += len(generated_output)
            
            # 保存样本结果
            results["sample_responses"].append({
                "instruction": instruction,
                "expected": expected_output,
                "generated": generated_output,
                "correct": is_correct
            })
        
        results["accuracy"] = results["correct_predictions"] / len(test_data[:10])
        results["average_response_length"] = total_length / len(test_data[:10])
        
        return results
    
    def _generate_response(self, instruction: str) -> str:
        """生成回答"""
        prompt = f"### 指令:\n{instruction}\n\n### 回答:\n"
        
        inputs = self.tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.model.generate(
                inputs.input_ids,
                max_new_tokens=512,
                temperature=0.7,
                top_p=0.9,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        # 提取回答部分
        if "### 回答:\n" in response:
            response = response.split("### 回答:\n")[1].strip()
        
        return response
    
    def _check_correctness(self, generated: str, expected: str) -> bool:
        """检查回答正确性（简化版）"""
        # 简单的关键词匹配检查
        expected_keywords = expected.lower().split()[:5]  # 取前5个词
        generated_lower = generated.lower()
        
        match_count = sum(1 for keyword in expected_keywords if keyword in generated_lower)
        return match_count >= len(expected_keywords) * 0.6  # 60%匹配度

def main():
    """主训练流程"""
    # 配置
    config = TrainingConfig(
        base_model_name="Qwen/Qwen2.5-7B-Instruct",
        output_dir="./models/water_ai_l1",
        num_epochs=3,
        batch_size=2,  # 根据GPU显存调整
        gradient_accumulation_steps=16
    )
    
    # 数据处理
    data_processor = WaterConservancyDataProcessor(config)
    
    # 加载现有RAG数据
    raw_data = data_processor.load_existing_rag_data("./tiaozhanbei-3/data")
    
    # 创建指令数据
    instruction_data = data_processor.create_instruction_data(raw_data)
    
    # 准备训练数据
    dataset = data_processor.prepare_training_data(instruction_data)
    
    # 模型训练
    trainer = WaterConservancyTrainer(config)
    
    # 加载基础模型
    model = trainer.load_base_model()
    
    # 设置LoRA
    model = trainer.setup_lora(model)
    
    # 开始训练
    trainer.train(model, dataset)
    
    # 模型评估
    evaluator = ModelEvaluator(config.output_dir)
    test_data = instruction_data[-50:]  # 使用最后50条作为测试
    eval_results = evaluator.evaluate_on_test_set(test_data)
    
    logger.info(f"评估结果: {eval_results}")
    
    # 保存评估结果
    with open(f"{config.output_dir}/evaluation_results.json", "w", encoding="utf-8") as f:
        json.dump(eval_results, f, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    main()

# 南水北调水力调度大模型微调配置文件

# 项目基本信息
project:
  name: "南水北调水力调度大模型"
  version: "1.0.0"
  description: "基于现有RAG系统的渐进式微调方案"

# 阶段配置
stages:
  # 阶段一：L0基座模型优化
  l0_base_optimization:
    enabled: true
    description: "替换API依赖，优化基础能力"
    
    model:
      base_model: "Qwen/Qwen2.5-7B-Instruct"  # 或 "Qwen/Qwen2.5-14B-Instruct"
      max_length: 4096
      torch_dtype: "float16"
      device_map: "auto"
    
    data:
      sources:
        - type: "existing_rag"
          path: "./tiaozhanbei-3/data"
          weight: 0.6
        - type: "general_chinese"
          path: "./data/general_chinese"
          weight: 0.2
        - type: "engineering_docs"
          path: "./data/engineering"
          weight: 0.2
      
      preprocessing:
        max_samples: 50000
        min_length: 50
        max_length: 2048
        quality_threshold: 0.7
    
    training:
      method: "lora"
      lora_config:
        r: 64
        alpha: 128
        dropout: 0.1
        target_modules: ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
      
      hyperparameters:
        num_epochs: 3
        batch_size: 4
        gradient_accumulation_steps: 8
        learning_rate: 2e-4
        warmup_ratio: 0.1
        weight_decay: 0.01
        lr_scheduler: "cosine"
      
      optimization:
        fp16: true
        gradient_checkpointing: true
        dataloader_pin_memory: true
    
    output:
      model_dir: "./models/l0_optimized"
      checkpoint_steps: 500
      eval_steps: 500
      logging_steps: 100

  # 阶段二：L1水利领域模型微调
  l1_domain_specialization:
    enabled: true
    description: "水利领域专业化微调"
    
    model:
      base_model: "./models/l0_optimized"  # 使用L0优化后的模型
      max_length: 4096
    
    data:
      sources:
        - type: "water_regulations"
          path: "./data/water_regulations"
          weight: 0.3
        - type: "engineering_cases"
          path: "./data/engineering_cases"
          weight: 0.3
        - type: "operation_records"
          path: "./data/operation_records"
          weight: 0.2
        - type: "technical_standards"
          path: "./data/technical_standards"
          weight: 0.2
      
      knowledge_graph:
        enabled: true
        entities_file: "./knowledge/water_entities.json"
        relations_file: "./knowledge/water_relations.json"
      
      instruction_templates:
        - "请解释以下水利工程概念：{concept}"
        - "根据以下条件分析水利工程运行状态：{conditions}"
        - "请提供关于{topic}的专业建议"
        - "分析以下水利数据的含义：{data}"
    
    training:
      method: "lora"
      lora_config:
        r: 64
        alpha: 128
        dropout: 0.1
      
      multi_task_learning:
        enabled: true
        tasks:
          - name: "domain_qa"
            weight: 0.4
          - name: "knowledge_reasoning"
            weight: 0.3
          - name: "document_understanding"
            weight: 0.2
          - name: "terminology_explanation"
            weight: 0.1
      
      hyperparameters:
        num_epochs: 5
        batch_size: 2
        gradient_accumulation_steps: 16
        learning_rate: 1e-4
        warmup_ratio: 0.1
    
    output:
      model_dir: "./models/l1_water_domain"

  # 阶段三：南水北调专用模型微调
  stn_specialized_model:
    enabled: true
    description: "南水北调专用调度模型"
    
    model:
      base_model: "./models/l1_water_domain"
      max_length: 6144  # 增加长度支持复杂调度场景
    
    data:
      sources:
        - type: "stn_operation_data"
          path: "./data/south_to_north/operations"
          weight: 0.4
        - type: "scheduling_cases"
          path: "./data/south_to_north/scheduling"
          weight: 0.3
        - type: "emergency_responses"
          path: "./data/south_to_north/emergency"
          weight: 0.2
        - type: "multimodal_data"
          path: "./data/south_to_north/multimodal"
          weight: 0.1
      
      specialized_tasks:
        - name: "scheduling_decision"
          description: "调度决策生成"
          input_format: "current_state + forecast -> scheduling_plan"
        - name: "risk_assessment"
          description: "风险评估"
          input_format: "plan + conditions -> risk_level"
        - name: "emergency_response"
          description: "应急响应"
          input_format: "emergency_situation -> response_plan"
        - name: "multimodal_analysis"
          description: "多模态数据分析"
          input_format: "text + image + timeseries -> analysis"
    
    training:
      method: "full_finetuning"  # 专用模型使用全参数微调
      
      reinforcement_learning:
        enabled: true
        reward_model: "scheduling_effectiveness"
        algorithm: "ppo"
        
      hyperparameters:
        num_epochs: 8
        batch_size: 1
        gradient_accumulation_steps: 32
        learning_rate: 5e-5
        warmup_ratio: 0.2
    
    output:
      model_dir: "./models/stn_specialized"

# 硬件和环境配置
hardware:
  gpu:
    required_memory: "24GB"  # 最低要求
    recommended_memory: "40GB"  # 推荐配置
    multi_gpu: true
    
  cpu:
    cores: 16
    memory: "64GB"
    
  storage:
    model_storage: "100GB"
    data_storage: "500GB"
    temp_storage: "200GB"

# 数据配置
data:
  preprocessing:
    tokenization:
      add_special_tokens: true
      padding: "max_length"
      truncation: true
      return_attention_mask: true
    
    augmentation:
      enabled: true
      methods:
        - "paraphrase"
        - "back_translation"
        - "synonym_replacement"
      
    quality_control:
      min_length: 20
      max_length: 4096
      language_detection: true
      duplication_removal: true
      content_filtering: true

# 训练监控
monitoring:
  tensorboard:
    enabled: true
    log_dir: "./logs/tensorboard"
    
  wandb:
    enabled: false
    project: "water_ai_finetuning"
    
  metrics:
    - "loss"
    - "perplexity"
    - "bleu_score"
    - "rouge_score"
    - "domain_accuracy"
    
  early_stopping:
    enabled: true
    patience: 5
    min_delta: 0.001
    monitor: "eval_loss"

# 评估配置
evaluation:
  test_sets:
    - name: "general_qa"
      path: "./test_data/general_qa.json"
      weight: 0.3
    - name: "domain_specific"
      path: "./test_data/water_domain.json"
      weight: 0.4
    - name: "scheduling_tasks"
      path: "./test_data/scheduling.json"
      weight: 0.3
  
  metrics:
    automatic:
      - "perplexity"
      - "bleu"
      - "rouge"
      - "bertscore"
    
    human_evaluation:
      enabled: true
      sample_size: 100
      criteria:
        - "accuracy"
        - "relevance"
        - "fluency"
        - "professional_quality"

# 部署配置
deployment:
  model_serving:
    framework: "vllm"  # 或 "transformers"
    max_concurrent_requests: 10
    timeout: 30
    
  api_integration:
    replace_existing_api: true
    fallback_enabled: true
    gradual_rollout: true
    
  monitoring:
    performance_tracking: true
    error_logging: true
    usage_analytics: true

# 安全和合规
security:
  data_privacy:
    anonymization: true
    access_control: true
    audit_logging: true
    
  model_security:
    input_validation: true
    output_filtering: true
    rate_limiting: true

# 实验配置
experiments:
  ablation_studies:
    enabled: true
    variables:
      - "lora_rank"
      - "learning_rate"
      - "data_mixing_ratio"
      - "training_epochs"
  
  hyperparameter_tuning:
    enabled: true
    method: "optuna"
    trials: 50
    
  model_comparison:
    baseline_models:
      - "original_rag_system"
      - "general_llm"
      - "domain_adapted_llm"

# 文档和报告
documentation:
  training_logs: true
  model_cards: true
  performance_reports: true
  user_guides: true
  
  output_formats:
    - "markdown"
    - "pdf"
    - "html"

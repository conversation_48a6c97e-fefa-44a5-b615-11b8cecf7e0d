"""
南水北调水力调度大模型框架API服务
提供RESTful API接口供外部系统调用
"""

import asyncio
import yaml
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
import uvicorn
import logging

from water_conservancy_ai_framework import (
    WaterConservancyAIFramework,
    MultiModalData,
    SchedulingDecision
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载配置
with open("config.yaml", "r", encoding="utf-8") as f:
    config = yaml.safe_load(f)

# 创建FastAPI应用
app = FastAPI(
    title="南水北调水力调度AI框架API",
    description="基于L0基座底板的多模态水利智能调度系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
if config['api']['cors']['enabled']:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config['api']['cors']['origins'],
        allow_credentials=True,
        allow_methods=config['api']['cors']['methods'],
        allow_headers=["*"],
    )

# 安全认证
security = HTTPBearer()

# 全局框架实例
framework: Optional[WaterConservancyAIFramework] = None

# Pydantic模型定义
class WaterLevelData(BaseModel):
    """水位数据模型"""
    station_id: str = Field(..., description="监测站ID")
    water_level: float = Field(..., description="水位(m)")
    timestamp: datetime = Field(default_factory=datetime.now)

class FlowRateData(BaseModel):
    """流量数据模型"""
    station_id: str = Field(..., description="监测站ID")
    flow_rate: float = Field(..., description="流量(m³/s)")
    timestamp: datetime = Field(default_factory=datetime.now)

class GateOperationData(BaseModel):
    """闸门操作数据模型"""
    gate_id: str = Field(..., description="闸门ID")
    opening_percentage: float = Field(..., ge=0, le=100, description="开度百分比")
    operation_time: datetime = Field(default_factory=datetime.now)

class CurrentStateRequest(BaseModel):
    """当前状态请求模型"""
    water_levels: List[WaterLevelData] = Field(default_factory=list)
    flow_rates: List[FlowRateData] = Field(default_factory=list)
    gate_operations: List[GateOperationData] = Field(default_factory=list)
    equipment_health: Optional[float] = Field(default=0.9, ge=0, le=1)
    weather_conditions: Optional[str] = Field(default="normal")

class ForecastDataRequest(BaseModel):
    """预测数据请求模型"""
    precipitation: Optional[float] = Field(default=0, description="预计降雨量(mm)")
    temperature: Optional[float] = Field(default=20, description="预计温度(°C)")
    seasonal_factor: Optional[float] = Field(default=1.0, description="季节因子")
    emergency_factor: Optional[float] = Field(default=1.0, description="应急因子")
    time_horizon: Optional[int] = Field(default=7, description="预测时间范围(天)")

class SchedulingRequest(BaseModel):
    """调度请求模型"""
    current_state: CurrentStateRequest
    forecast_data: ForecastDataRequest

class RealtimeDataRequest(BaseModel):
    """实时数据请求模型"""
    text_data: Optional[str] = Field(None, description="文本数据")
    image_data: Optional[str] = Field(None, description="图像数据(base64编码)")
    timeseries_data: Optional[Dict] = Field(None, description="时序数据")
    metadata: Optional[Dict] = Field(default_factory=dict)

class SchedulingResponse(BaseModel):
    """调度响应模型"""
    decision_type: str
    water_allocation: Dict[str, float]
    gate_operations: Dict[str, Any]
    risk_level: float
    confidence: float
    reasoning: str
    emergency_plan: Optional[Dict] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class AnalysisResponse(BaseModel):
    """分析响应模型"""
    status: str
    analysis_result: Dict[str, Any]
    timestamp: datetime
    processing_time: Optional[str] = None

class HealthResponse(BaseModel):
    """健康检查响应模型"""
    overall_status: str
    components: Dict[str, str]
    performance_metrics: Dict[str, str]
    last_check: datetime

# 依赖注入函数
async def get_framework() -> WaterConservancyAIFramework:
    """获取框架实例"""
    global framework
    if framework is None:
        raise HTTPException(status_code=503, detail="框架未初始化")
    return framework

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证访问令牌"""
    if not config['api']['authentication']['enabled']:
        return True
    
    # 这里可以添加JWT令牌验证逻辑
    token = credentials.credentials
    if token != "demo_token":  # 简化的验证逻辑
        raise HTTPException(status_code=401, detail="无效的访问令牌")
    return True

# API路由定义
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global framework
    logger.info("正在启动南水北调水力调度AI框架...")
    
    try:
        framework = WaterConservancyAIFramework(config)
        await framework.initialize()
        logger.info("框架启动成功")
    except Exception as e:
        logger.error(f"框架启动失败: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("正在关闭南水北调水力调度AI框架...")

@app.get("/", response_model=Dict[str, str])
async def root():
    """根路径"""
    return {
        "message": "南水北调水力调度AI框架API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check(framework: WaterConservancyAIFramework = Depends(get_framework)):
    """健康检查"""
    try:
        health_status = await framework.monitor_system_health()
        return HealthResponse(**health_status)
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/scheduling/decision", response_model=SchedulingResponse)
async def generate_scheduling_decision(
    request: SchedulingRequest,
    background_tasks: BackgroundTasks,
    framework: WaterConservancyAIFramework = Depends(get_framework),
    _: bool = Depends(verify_token)
):
    """生成调度决策"""
    try:
        logger.info("收到调度决策请求")
        
        # 转换请求数据格式
        current_state = {
            'water_levels': {item.station_id: item.water_level for item in request.current_state.water_levels},
            'flow_rates': {item.station_id: item.flow_rate for item in request.current_state.flow_rates},
            'gate_operations': {item.gate_id: item.opening_percentage for item in request.current_state.gate_operations},
            'equipment_health': request.current_state.equipment_health,
            'weather_conditions': request.current_state.weather_conditions
        }
        
        forecast_data = {
            'precipitation': request.forecast_data.precipitation,
            'temperature': request.forecast_data.temperature,
            'seasonal_factor': request.forecast_data.seasonal_factor,
            'emergency_factor': request.forecast_data.emergency_factor
        }
        
        # 生成调度决策
        decision = await framework.generate_scheduling_decision(
            current_state=current_state,
            forecast_data=forecast_data,
            time_horizon=request.forecast_data.time_horizon
        )
        
        # 后台任务：记录决策日志
        background_tasks.add_task(log_scheduling_decision, decision)
        
        return SchedulingResponse(
            decision_type=decision.decision_type,
            water_allocation=decision.water_allocation,
            gate_operations=decision.gate_operations,
            risk_level=decision.risk_level,
            confidence=decision.confidence,
            reasoning=decision.reasoning,
            emergency_plan=decision.emergency_plan
        )
        
    except Exception as e:
        logger.error(f"调度决策生成失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/analysis/realtime", response_model=AnalysisResponse)
async def analyze_realtime_data(
    request: RealtimeDataRequest,
    framework: WaterConservancyAIFramework = Depends(get_framework),
    _: bool = Depends(verify_token)
):
    """实时数据分析"""
    try:
        logger.info("收到实时数据分析请求")
        
        # 构建数据流
        data_stream = {
            'text': request.text_data,
            'image': request.image_data,
            'timeseries': request.timeseries_data,
            'metadata': request.metadata
        }
        
        # 处理实时数据
        start_time = datetime.now()
        result = await framework.process_realtime_data(data_stream)
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return AnalysisResponse(
            status=result['status'],
            analysis_result=result.get('analysis_result', {}),
            timestamp=result['timestamp'],
            processing_time=f"{processing_time:.3f}s"
        )
        
    except Exception as e:
        logger.error(f"实时数据分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/monitoring/status")
async def get_monitoring_status(
    framework: WaterConservancyAIFramework = Depends(get_framework),
    _: bool = Depends(verify_token)
):
    """获取监控状态"""
    try:
        # 这里可以返回详细的监控数据
        return {
            "system_status": "operational",
            "active_connections": 1,
            "requests_processed": 100,
            "average_response_time": "0.5s",
            "last_update": datetime.now()
        }
    except Exception as e:
        logger.error(f"获取监控状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/config")
async def get_configuration(_: bool = Depends(verify_token)):
    """获取系统配置（脱敏）"""
    try:
        # 返回脱敏的配置信息
        safe_config = {
            "system": config.get("system", {}),
            "api": {
                "host": config["api"]["host"],
                "port": config["api"]["port"]
            },
            "models": {
                "l0_base": {
                    "embedding_model": config["models"]["l0_base"]["embedding_model"],
                    "device": config["models"]["l0_base"]["device"]
                }
            }
        }
        return safe_config
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 后台任务函数
async def log_scheduling_decision(decision: SchedulingDecision):
    """记录调度决策日志"""
    try:
        # 这里可以将决策记录到数据库或日志文件
        logger.info(f"调度决策已记录: {decision.decision_type}, 风险等级: {decision.risk_level}")
    except Exception as e:
        logger.error(f"记录调度决策失败: {e}")

# WebSocket支持（可选）
@app.websocket("/ws/realtime")
async def websocket_endpoint(websocket):
    """WebSocket实时数据推送"""
    await websocket.accept()
    try:
        while True:
            # 这里可以推送实时监控数据
            data = {
                "timestamp": datetime.now().isoformat(),
                "water_level": 105.5,
                "flow_rate": 280.0,
                "status": "normal"
            }
            await websocket.send_json(data)
            await asyncio.sleep(5)  # 每5秒推送一次
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
    finally:
        await websocket.close()

if __name__ == "__main__":
    # 直接运行API服务
    uvicorn.run(
        "api:app",
        host=config['api']['host'],
        port=config['api']['port'],
        workers=config['api']['workers'],
        reload=True if config['system']['environment'] == 'development' else False
    )
